import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import AppNavigator from './src/navigation/AppNavigator';
import { UserPreferences } from './src/types';
import { loadUserPreferences, saveUserPreferences } from './src/utils/storage';

export default function App() {
  const [userPreferences, setUserPreferences] = useState<UserPreferences | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadPreferences();
  }, []);

  const loadPreferences = async () => {
    try {
      const preferences = await loadUserPreferences();
      setUserPreferences(preferences);
    } catch (error) {
      console.error('Error loading preferences:', error);
      // Fallback to default preferences
      setUserPreferences({
        likedMeals: [],
        dislikedMeals: [],
        dietaryRestrictions: [],
        allergies: [],
        preferredCuisines: [],
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdatePreferences = async (preferences: UserPreferences) => {
    setUserPreferences(preferences);
    await saveUserPreferences(preferences);
  };

  if (isLoading || !userPreferences) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>MatchMeal</Text>
        <Text style={styles.loadingSubtext}>Chargement...</Text>
      </View>
    );
  }

  return (
    <GestureHandlerRootView style={styles.container}>
      <AppNavigator
        userPreferences={userPreferences}
        onUpdatePreferences={handleUpdatePreferences}
      />
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FF6B35',
    marginBottom: 10,
  },
  loadingSubtext: {
    fontSize: 16,
    color: '#666',
  },
});
