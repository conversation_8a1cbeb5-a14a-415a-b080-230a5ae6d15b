import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  Text,
  TouchableOpacity,
  Alert,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import SwipeableCard from '../components/SwipeableCard';
import AnimatedButton from '../components/AnimatedButton';
import { Meal, UserPreferences } from '../types';
import { mealsData } from '../data/meals';

interface DiscoverScreenProps {
  navigation: any;
  userPreferences: UserPreferences;
  onUpdatePreferences: (preferences: UserPreferences) => void;
}

const DiscoverScreen: React.FC<DiscoverScreenProps> = ({
  navigation,
  userPreferences,
  onUpdatePreferences,
}) => {
  const [currentMeals, setCurrentMeals] = useState<Meal[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    loadMeals();
  }, []);

  const loadMeals = () => {
    // Filtrer les repas déjà vus (likés ou dislikés)
    const seenMealIds = [
      ...userPreferences.likedMeals,
      ...userPreferences.dislikedMeals,
    ];
    
    const availableMeals = mealsData.filter(
      meal => !seenMealIds.includes(meal.id)
    );

    if (availableMeals.length === 0) {
      // Tous les repas ont été vus, on peut proposer de recommencer
      Alert.alert(
        'Plus de repas !',
        'Vous avez vu tous les repas disponibles. Voulez-vous recommencer ?',
        [
          {
            text: 'Voir mes favoris',
            onPress: () => navigation.navigate('Favorites'),
          },
          {
            text: 'Recommencer',
            onPress: () => {
              const resetPreferences = {
                ...userPreferences,
                dislikedMeals: [],
              };
              onUpdatePreferences(resetPreferences);
              setCurrentMeals([...mealsData]);
              setCurrentIndex(0);
            },
          },
        ]
      );
      return;
    }

    setCurrentMeals(availableMeals);
    setCurrentIndex(0);
  };

  const handleSwipeRight = (meal: Meal) => {
    const updatedPreferences = {
      ...userPreferences,
      likedMeals: [...userPreferences.likedMeals, meal.id],
    };
    onUpdatePreferences(updatedPreferences);
    
    setTimeout(() => {
      setCurrentIndex(prev => prev + 1);
      if (currentIndex >= currentMeals.length - 2) {
        loadMeals();
      }
    }, 300);
  };

  const handleSwipeLeft = (meal: Meal) => {
    const updatedPreferences = {
      ...userPreferences,
      dislikedMeals: [...userPreferences.dislikedMeals, meal.id],
    };
    onUpdatePreferences(updatedPreferences);
    
    setTimeout(() => {
      setCurrentIndex(prev => prev + 1);
      if (currentIndex >= currentMeals.length - 2) {
        loadMeals();
      }
    }, 300);
  };

  const handleCardPress = (meal: Meal) => {
    navigation.navigate('RecipeDetail', { meal });
  };

  const handleLikePress = () => {
    if (currentIndex < currentMeals.length) {
      handleSwipeRight(currentMeals[currentIndex]);
    }
  };

  const handleDislikePress = () => {
    if (currentIndex < currentMeals.length) {
      handleSwipeLeft(currentMeals[currentIndex]);
    }
  };

  const renderCards = () => {
    return currentMeals
      .slice(currentIndex, currentIndex + 3)
      .map((meal, index) => (
        <SwipeableCard
          key={`${meal.id}-${currentIndex + index}`}
          meal={meal}
          onSwipeLeft={handleSwipeLeft}
          onSwipeRight={handleSwipeRight}
          onPress={() => handleCardPress(meal)}
          isTop={index === 0}
        />
      ))
      .reverse(); // Reverse pour que la première carte soit au-dessus
  };

  if (currentMeals.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" />
        <View style={styles.emptyContainer}>
          <Ionicons name="restaurant-outline" size={80} color="#ccc" />
          <Text style={styles.emptyText}>Chargement des repas...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>MatchMeal</Text>
        <Text style={styles.subtitle}>
          Découvrez votre prochain repas
        </Text>
      </View>

      {/* Cards Stack */}
      <View style={styles.cardsContainer}>
        {renderCards()}
      </View>

      {/* Action Buttons */}
      <View style={styles.actionsContainer}>
        <AnimatedButton
          onPress={handleDislikePress}
          iconName="close"
          backgroundColor="#F44336"
          disabled={currentIndex >= currentMeals.length}
        />

        <TouchableOpacity
          style={styles.infoButton}
          onPress={() => {
            if (currentIndex < currentMeals.length) {
              handleCardPress(currentMeals[currentIndex]);
            }
          }}
          disabled={currentIndex >= currentMeals.length}
        >
          <Ionicons name="information-circle-outline" size={24} color="#666" />
        </TouchableOpacity>

        <AnimatedButton
          onPress={handleLikePress}
          iconName="heart"
          backgroundColor="#4CAF50"
          disabled={currentIndex >= currentMeals.length}
        />
      </View>

      {/* Progress indicator */}
      <View style={styles.progressContainer}>
        <Text style={styles.progressText}>
          {Math.min(currentIndex + 1, currentMeals.length)} / {currentMeals.length}
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FF6B35',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  cardsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 30,
  },

  infoButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 30,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  progressContainer: {
    alignItems: 'center',
    paddingBottom: 20,
  },
  progressText: {
    fontSize: 14,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    marginTop: 20,
  },
});

export default DiscoverScreen;
