import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';

import DiscoverScreen from '../screens/DiscoverScreen';
import FavoritesScreen from '../screens/FavoritesScreen';
import RecipeDetailScreen from '../screens/RecipeDetailScreen';
import { UserPreferences } from '../types';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

interface AppNavigatorProps {
  userPreferences: UserPreferences;
  onUpdatePreferences: (preferences: UserPreferences) => void;
}

const MainTabs: React.FC<AppNavigatorProps> = ({ userPreferences, onUpdatePreferences }) => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Discover') {
            iconName = focused ? 'restaurant' : 'restaurant-outline';
          } else if (route.name === 'Favorites') {
            iconName = focused ? 'heart' : 'heart-outline';
          } else {
            iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#FF6B35',
        tabBarInactiveTintColor: '#666',
        tabBarStyle: {
          backgroundColor: '#fff',
          borderTopWidth: 1,
          borderTopColor: '#eee',
          paddingTop: 5,
          paddingBottom: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Discover" 
        options={{ tabBarLabel: 'Découvrir' }}
      >
        {(props) => (
          <DiscoverScreen
            {...props}
            userPreferences={userPreferences}
            onUpdatePreferences={onUpdatePreferences}
          />
        )}
      </Tab.Screen>
      
      <Tab.Screen 
        name="Favorites" 
        options={{ tabBarLabel: 'Favoris' }}
      >
        {(props) => (
          <FavoritesScreen
            {...props}
            userPreferences={userPreferences}
            onUpdatePreferences={onUpdatePreferences}
          />
        )}
      </Tab.Screen>
    </Tab.Navigator>
  );
};

const AppNavigator: React.FC<AppNavigatorProps> = ({ userPreferences, onUpdatePreferences }) => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen name="MainTabs">
          {(props) => (
            <MainTabs
              {...props}
              userPreferences={userPreferences}
              onUpdatePreferences={onUpdatePreferences}
            />
          )}
        </Stack.Screen>
        
        <Stack.Screen 
          name="RecipeDetail" 
          component={RecipeDetailScreen}
          options={{
            presentation: 'modal',
            gestureEnabled: true,
            gestureDirection: 'vertical',
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
