import React from 'react';
import {
  View,
  Dimensions,
  StyleSheet,
  Text,
  PanResponder,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import { Meal } from '../types';
import MealCard from './MealCard';
import { Ionicons } from '@expo/vector-icons';

const { width: screenWidth } = Dimensions.get('window');

interface SwipeableCardProps {
  meal: Meal;
  onSwipeLeft: (meal: Meal) => void;
  onSwipeRight: (meal: Meal) => void;
  onPress?: () => void;
  isTop?: boolean;
}

const SWIPE_THRESHOLD = screenWidth * 0.3;
const ROTATION_ANGLE = 30;

const SwipeableCard: React.FC<SwipeableCardProps> = ({
  meal,
  onSwipeLeft,
  onSwipeRight,
  onPress,
  isTop = false,
}) => {
  const scale = useSharedValue(isTop ? 1 : 0.95);
  const opacity = useSharedValue(isTop ? 1 : 0.8);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
      opacity: opacity.value,
    };
  });

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <MealCard meal={meal} onPress={onPress} />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    alignSelf: 'center',
  },
});

export default SwipeableCard;
