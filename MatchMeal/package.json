{"name": "matchmeal", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^15.0.2", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.7", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.8", "expo": "~54.0.9", "expo-status-bar": "~3.0.8", "react": "19.1.0", "react-native": "0.81.4", "react-native-gesture-handler": "^2.28.0", "react-native-reanimated": "^4.1.0", "react-native-vector-icons": "^10.3.0"}, "devDependencies": {"@types/react": "~19.1.0", "typescript": "~5.9.2"}, "private": true}